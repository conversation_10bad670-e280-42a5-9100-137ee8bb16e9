#!/usr/bin/env python3
"""
Script de démonstration pour montrer comment utiliser les scripts modifiés en ligne de commande.
"""

import subprocess
import sys

def demo_inference():
    """Démontre l'utilisation du script d'inférence."""
    print("\n=== Démonstration : Script d'inférence ===")
    print("Commande exemple :")
    cmd = [
        sys.executable, "infer_nnunet.py",
        "--dataset_id", "018",
        "--configuration", "2d", 
        "--fold", "all",
        "--epochs", "5",
        "--gpu_id", "0",
        "--input_folder", r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\imagesTestInference",
        "--base_output_root", r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference"
    ]
    print(" ".join(cmd))
    print("\n(Cette commande n'est pas exécutée dans la démo)")

def demo_training():
    """Démontre l'utilisation du script d'entraînement."""
    print("\n=== Démonstration : Script d'entraînement ===")
    print("Commande exemple :")
    cmd = [
        sys.executable, "train_nnunet.py",
        "--dataset_id", "017",
        "--configuration", "3d_fullres",
        "--fold", "all", 
        "--epochs", "5",
        "--gpu_id", "0",
        "--num_gpus", "1",
        "--save_npz"
    ]
    print(" ".join(cmd))
    print("\n(Cette commande n'est pas exécutée dans la démo)")

def demo_export():
    """Démontre l'utilisation du script d'export."""
    print("\n=== Démonstration : Script d'export ===")
    print("Commande exemple :")
    cmd = [
        sys.executable, "export_model_zip.py",
        "--dataset_id", "014",
        "--configuration", "2d",
        "--fold", "all",
        "--trainer_name", "nnUNetTrainer_5epochs",
        "--plans_name", "nnUNetPlans", 
        "--output_zip_name", "model_014_2d.zip"
    ]
    print(" ".join(cmd))
    print("\n(Cette commande n'est pas exécutée dans la démo)")

def main():
    print("🎯 Démonstration des scripts modifiés")
    print("=" * 50)
    
    demo_inference()
    demo_training() 
    demo_export()
    
    print("\n" + "=" * 50)
    print("✅ Tous les scripts supportent maintenant les arguments en ligne de commande")
    print("🖥️  Utilisez 'python gui_launcher.py' pour l'interface graphique")
    print("📖 Consultez README_GUI.md pour plus d'informations")

if __name__ == "__main__":
    main()
