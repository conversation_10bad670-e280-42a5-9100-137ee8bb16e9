import os
import subprocess
import argparse
from utils.conversion import convert_images_to_grayscale, convert_labels_to_classes
from utils.rename import rename_imagesTr, rename_labelsTr
from utils.verification import check_image, verify_dataset_integrity

def parse_arguments():
    parser = argparse.ArgumentParser(description="nnU-Net Training Script")
    parser.add_argument("--dataset_id", default="017", help="Dataset ID")
    parser.add_argument("--configuration", default="3d_fullres", choices=["2d", "3d_fullres"], help="Configuration")
    parser.add_argument("--fold", default="all", help="Fold (all, 0-4)")
    parser.add_argument("--epochs", type=int, default=5, help="Number of epochs")
    parser.add_argument("--gpu_id", default="0", help="GPU ID")
    parser.add_argument("--num_gpus", type=int, default=1, help="Number of GPUs")
    parser.add_argument("--save_npz", action="store_true", help="Save NPZ files")
    parser.add_argument("--continue_training", action="store_true", help="Continue training")
    return parser.parse_args()

def main():
    # Parse arguments
    args = parse_arguments()

    # === CONFIGURATION GÉNÉRALE ===
    DATASET_ID = args.dataset_id
    CONFIGURATION = args.configuration
    FOLD = args.fold
    PLANS_NAME = "nnUNetPlans"
    EPOCHS = args.epochs
    GPU_ID = args.gpu_id
    NUM_GPUS = args.num_gpus

    # === HYPERPARAMÈTRES ENTRAÎNEMENT ===
    SAVE_NPZ = args.save_npz
    CONTINUE_TRAINING = args.continue_training

    # === PATHS ===
    RAW_PATH = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw"
    PREPROCESSED_PATH = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_preprocessed"
    RESULTS_PATH = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\nnUNet_results"

    # === ENV VARS ===
    os.environ["nnUNet_raw"] = RAW_PATH
    os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
    os.environ["nnUNet_results"] = RESULTS_PATH
    os.environ["CUDA_VISIBLE_DEVICES"] = GPU_ID
    os.environ["nnUNet_n_proc_DA"] = "2"

    def run(cmd):
        print(f"\n[RUN] Lancement : {cmd}\n")
        subprocess.run(cmd, shell=True, check=True)

    # === 0. Prétraitement images + masques (non destructif) ===
    # 🔍 Trouver automatiquement le dossier DatasetXXX_nom
    dataset_prefix = f"Dataset{DATASET_ID}_"
    dataset_name = next(
        (d for d in os.listdir(RAW_PATH) if d.startswith(dataset_prefix)),
        None
    )

    if dataset_name is None:
        raise FileNotFoundError(f"[ERROR] Aucun dossier commençant par '{dataset_prefix}' trouvé dans {RAW_PATH}")

    # Construction des chemins dynamiques
    images_dir = os.path.join(RAW_PATH, dataset_name, "imagesTr")
    labels_dir = os.path.join(RAW_PATH, dataset_name, "labelsTr")

    print(f"[INFO] images_dir = {images_dir}")
    print(f"[INFO] labels_dir = {labels_dir}")

    verify_dataset_integrity(
        imagesTr_dir=images_dir,
        labelsTr_dir=labels_dir
    )

    rename_imagesTr(images_dir)
    rename_labelsTr(labels_dir)
    convert_images_to_grayscale(images_dir)  # optionnel : output_dir="..."
    convert_labels_to_classes(labels_dir, {
        0: 0,    # background
        29: 1,   # frontwall
        149: 2,  # backwall
        76: 3,  # flaw
        125: 4   # indication
    })  # optionnel : output_dir="..."

    # === 1. Preprocessing (plans + vérification) ===
    run(f'nnUNetv2_plan_and_preprocess -d {DATASET_ID} --verify_dataset_integrity')

    # === 2. Entraînement avec le bon Trainer prédéfini ===
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    train_cmd = f'nnUNetv2_train {DATASET_ID} {CONFIGURATION} {FOLD} -p {PLANS_NAME} -tr {trainer_class}'
    if SAVE_NPZ:
        train_cmd += " --npz"
    if CONTINUE_TRAINING:
        train_cmd += " --c"
    if NUM_GPUS > 1:
        train_cmd += f" -num_gpus {NUM_GPUS}"

    run(train_cmd)


if __name__ == "__main__":
    main()
