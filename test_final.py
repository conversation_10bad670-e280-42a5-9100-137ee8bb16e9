#!/usr/bin/env python3
"""
Test final pour vérifier que tous les scripts fonctionnent correctement.
"""

import subprocess
import sys
import tempfile
import os

def test_all_scripts():
    """Teste tous les scripts avec --help pour vérifier qu'ils fonctionnent."""
    scripts = [
        ("infer_nnunet.py", "Script d'inférence"),
        ("train_nnunet.py", "Script d'entraînement"), 
        ("export_model_zip.py", "Script d'export")
    ]
    
    print("=" * 60)
    print("TEST FINAL - VÉRIFICATION DE TOUS LES SCRIPTS")
    print("=" * 60)
    
    all_success = True
    
    for script_name, description in scripts:
        print(f"\n[TEST] {description} ({script_name})")
        print("-" * 40)
        
        try:
            # Test --help
            result = subprocess.run([sys.executable, script_name, "--help"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"[OK] --help fonctionne")
                
                # Vérifier qu'il n'y a pas d'erreur d'encodage
                if "UnicodeEncodeError" in result.stderr:
                    print(f"[ERROR] Erreur d'encodage détectée")
                    all_success = False
                else:
                    print(f"[OK] Pas d'erreur d'encodage")
                    
                # Afficher un extrait de l'aide
                help_lines = result.stdout.split('\n')[:5]
                print(f"[INFO] Aide: {' '.join(help_lines)}")
                
            else:
                print(f"[ERROR] --help a échoué (code {result.returncode})")
                print(f"STDERR: {result.stderr}")
                all_success = False
                
        except subprocess.TimeoutExpired:
            print(f"[ERROR] Timeout lors du test de {script_name}")
            all_success = False
        except Exception as e:
            print(f"[ERROR] Erreur inattendue: {e}")
            all_success = False
    
    print("\n" + "=" * 60)
    if all_success:
        print("[SUCCESS] ✓ TOUS LES TESTS SONT PASSÉS !")
        print("✓ Tous les scripts acceptent les arguments CLI")
        print("✓ Aucune erreur d'encodage détectée")
        print("✓ L'interface graphique peut être utilisée")
    else:
        print("[FAILED] ✗ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Vérifiez les erreurs ci-dessus")
    print("=" * 60)
    
    return all_success

def test_gui_import():
    """Teste que l'interface graphique peut être importée."""
    print("\n[TEST] Test d'import de l'interface graphique")
    print("-" * 40)
    
    try:
        # Test d'import sans lancer l'interface
        result = subprocess.run([sys.executable, "-c", 
                               "import gui_launcher; print('[OK] Import réussi')"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("[OK] L'interface graphique peut être importée")
            return True
        else:
            print(f"[ERROR] Erreur d'import: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Erreur lors du test d'import: {e}")
        return False

def main():
    print("🧪 TEST FINAL DE L'INTERFACE NNUNET")
    
    # Test des scripts
    scripts_ok = test_all_scripts()
    
    # Test de l'interface
    gui_ok = test_gui_import()
    
    print("\n" + "=" * 60)
    print("RÉSUMÉ FINAL")
    print("=" * 60)
    
    if scripts_ok and gui_ok:
        print("🎉 SUCCÈS COMPLET !")
        print("✓ Tous les scripts fonctionnent correctement")
        print("✓ L'interface graphique est prête à être utilisée")
        print("\n📋 INSTRUCTIONS D'UTILISATION:")
        print("1. Lancez l'interface: python gui_launcher.py")
        print("2. Sélectionnez l'onglet approprié (Inférence/Entraînement/Export)")
        print("3. Configurez les paramètres")
        print("4. Cliquez sur le bouton de lancement")
        print("5. Surveillez les logs en temps réel")
        
        print("\n📁 FICHIERS CRÉÉS:")
        print("- gui_launcher.py (interface graphique)")
        print("- *_backup.py (sauvegardes des scripts originaux)")
        print("- test_*.py (scripts de test)")
        print("- README_GUI.md (documentation)")
        
    else:
        print("❌ ÉCHEC")
        if not scripts_ok:
            print("✗ Problèmes avec les scripts")
        if not gui_ok:
            print("✗ Problèmes avec l'interface graphique")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
