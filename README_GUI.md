# Interface Graphique nnU-Net

Cette interface graphique permet de lancer facilement les scripts nnU-Net avec une interface conviviale.

## 📁 Fichiers

### Scripts principaux
- `gui_launcher.py` - Interface graphique principale
- `infer_nnunet.py` - Script d'inférence (modifié pour accepter les arguments CLI)
- `train_nnunet.py` - Script d'entraînement (modifié pour accepter les arguments CLI)
- `export_model_zip.py` - Script d'export de modèle (modifié pour accepter les arguments CLI)

### Sauvegardes
- `infer_nnunet_backup.py` - Sauvegarde du script d'inférence original
- `train_nnunet_backup.py` - Sauvegarde du script d'entraînement original
- `export_model_zip_backup.py` - Sauvegarde du script d'export original

### Utilitaires
- `test_scripts.py` - Script de test pour vérifier le bon fonctionnement
- `README_GUI.md` - Ce fichier de documentation

## 🚀 Utilisation

### Lancement de l'interface
```bash
python gui_launcher.py
```

### Fonctionnalités

#### Onglet Inférence
- **Dataset ID** : Identifiant du dataset (ex: 018)
- **Configuration** : 2d ou 3d_fullres
- **Fold** : all ou 0-4
- **Epochs** : Nombre d'époques (0 pour le trainer par défaut)
- **GPU ID** : Identifiant du GPU à utiliser
- **Dossier d'entrée** : Dossier contenant les images à traiter
- **Dossier de sortie (base)** : Dossier de base pour les résultats

#### Onglet Entraînement
- **Dataset ID** : Identifiant du dataset
- **Configuration** : 2d ou 3d_fullres
- **Fold** : all ou 0-4
- **Epochs** : Nombre d'époques d'entraînement
- **GPU ID** : Identifiant du GPU à utiliser
- **Nombre de GPUs** : Nombre de GPUs à utiliser
- **Options** :
  - Sauvegarder NPZ : Sauvegarde les fichiers NPZ
  - Continuer l'entraînement : Continue un entraînement existant

#### Onglet Export Modèle
- **Dataset ID** : Identifiant du dataset
- **Configuration** : 2d ou 3d_fullres
- **Fold** : all ou 0-4
- **Nom du trainer** : Nom du trainer utilisé (ex: nnUNetTrainer_5epochs)
- **Plans name** : Nom des plans (généralement nnUNetPlans)
- **Nom du fichier ZIP** : Nom du fichier ZIP de sortie

### Zone de logs
- Affiche les logs en temps réel lors de l'exécution des scripts
- Bouton "Effacer les logs" pour nettoyer l'affichage

## 🔧 Modifications apportées

### Scripts modifiés
Tous les scripts ont été modifiés pour accepter les arguments en ligne de commande via `argparse` :

1. **infer_nnunet.py** :
   - Ajout de `parse_arguments()` pour gérer les paramètres CLI
   - Encapsulation du code principal dans `main()`
   - Arguments requis : `--input_folder`, `--base_output_root`

2. **train_nnunet.py** :
   - Ajout de `parse_arguments()` pour gérer les paramètres CLI
   - Encapsulation du code principal dans `main()`
   - Support des flags `--save_npz` et `--continue_training`

3. **export_model_zip.py** :
   - Ajout de `parse_arguments()` pour gérer les paramètres CLI
   - Encapsulation du code principal dans `main()`
   - Argument requis : `--output_zip_name`

### Interface graphique
- Interface à onglets pour organiser les différents scripts
- Validation des paramètres requis avant lancement
- Exécution des scripts dans des threads séparés pour éviter le blocage de l'interface
- Affichage des logs en temps réel
- Valeurs par défaut basées sur les scripts originaux

## 🧪 Tests

Pour tester que les scripts fonctionnent correctement :
```bash
python test_scripts.py
```

## 📝 Notes importantes

1. **Compatibilité** : Les scripts modifiés restent compatibles avec l'utilisation en ligne de commande
2. **Sauvegardes** : Les scripts originaux sont sauvegardés avec le suffixe `_backup.py`
3. **Exécution** : Les scripts sont lancés dans le terminal où l'interface est démarrée
4. **Logs** : Tous les logs des scripts sont affichés dans l'interface en temps réel

## 🔄 Restauration

Pour restaurer les scripts originaux :
```bash
copy infer_nnunet_backup.py infer_nnunet.py
copy train_nnunet_backup.py train_nnunet.py
copy export_model_zip_backup.py export_model_zip.py
```
