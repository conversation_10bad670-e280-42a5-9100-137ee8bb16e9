#!/usr/bin/env python3
"""
Script de test pour vérifier que l'encodage fonctionne correctement.
"""

import subprocess
import sys
import tempfile
import os

def test_encoding():
    """Teste l'encodage en créant des dossiers temporaires et en lançant le script d'inférence."""
    print("[TEST] Test de l'encodage des scripts...")
    
    # Créer des dossiers temporaires
    with tempfile.TemporaryDirectory() as temp_dir:
        input_folder = os.path.join(temp_dir, "input")
        output_folder = os.path.join(temp_dir, "output")
        
        os.makedirs(input_folder, exist_ok=True)
        os.makedirs(output_folder, exist_ok=True)
        
        # Créer un fichier image factice
        test_image = os.path.join(input_folder, "test_0000.png")
        with open(test_image, "w") as f:
            f.write("fake image content")
        
        print(f"[INFO] Dossier d'entrée temporaire : {input_folder}")
        print(f"[INFO] Dossier de sortie temporaire : {output_folder}")
        
        # Tester le script d'inférence avec des paramètres valides
        cmd = [
            sys.executable, "infer_nnunet.py",
            "--dataset_id", "018",
            "--configuration", "2d",
            "--fold", "all", 
            "--epochs", "5",
            "--gpu_id", "0",
            "--input_folder", input_folder,
            "--base_output_root", output_folder
        ]
        
        print("[TEST] Lancement du test d'encodage...")
        print(f"[CMD] {' '.join(cmd)}")
        
        try:
            # Lancer le script avec un timeout court pour éviter l'exécution complète
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if "UnicodeEncodeError" in result.stderr:
                print("[ERROR] Erreur d'encodage détectée !")
                print("STDERR:", result.stderr)
                return False
            else:
                print("[SUCCESS] Aucune erreur d'encodage détectée")
                print("Le script a commencé à s'exécuter correctement")
                if result.stdout:
                    print("STDOUT:", result.stdout[:200] + "..." if len(result.stdout) > 200 else result.stdout)
                return True
                
        except subprocess.TimeoutExpired:
            print("[INFO] Timeout atteint - c'est normal, le script fonctionne")
            return True
        except Exception as e:
            print(f"[ERROR] Erreur inattendue : {e}")
            return False

def main():
    print("=" * 60)
    print("TEST D'ENCODAGE DES SCRIPTS MODIFIÉS")
    print("=" * 60)
    
    success = test_encoding()
    
    print("\n" + "=" * 60)
    if success:
        print("[SUCCESS] ✓ Test d'encodage réussi !")
        print("Les scripts peuvent maintenant être utilisés avec l'interface graphique.")
    else:
        print("[FAILED] ✗ Test d'encodage échoué !")
        print("Il reste des problèmes d'encodage à résoudre.")
    print("=" * 60)

if __name__ == "__main__":
    main()
