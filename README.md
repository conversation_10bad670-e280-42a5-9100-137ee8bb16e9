# nnU-Net pour la Segmentation d'Images de Corrosion

## Description
Ce projet implémente la segmentation d'images de corrosion en utilisant nnU-Net, un framework de deep learning spécialisé dans la segmentation d'images médicales. Le projet est configuré pour détecter et segmenter différentes classes de corrosion :
- Frontwall (29)
- Backwall (149)
- Flaw (76)
- Indication (125)

## Structure du Projet
```
unet/
├── utils/                 # Fonctions utilitaires
│   ├── __init__.py        # Module d'initialisation
│   ├── conversion.py      # Conversion des images et labels
│   ├── overlay_manager.py # Gestion des overlays et visualisations
│   ├── rename.py          # Renommage des fichiers
│   ├── verification.py    # Vérification de l'intégrité des données
│   └── visualisation.py   # Outils de visualisation
├── train_nnunet.py        # Script d'entraînement nnU-Net
├── infer_nnunet.py        # Script d'inférence nnU-Net
├── export_model_zip.py    # Export du modèle entraîné
└── cmd export.txt         # Commandes d'export
```

## Prérequis
- Python 3.8+
- nnU-Net v2
- PyTorch
- NumPy
- OpenCV
- Matplotlib

## Configuration des Variables d'Environnement
```bash
export nnUNet_raw="chemin/vers/nnUNet_raw"
export nnUNet_preprocessed="chemin/vers/nnUNet_preprocessed"
export nnUNet_results="chemin/vers/nnUNet_results"
```

## Utilisation

### Entraînement
```bash
python train_nnunet.py
```
Le script d'entraînement :
- Prétraite les images (conversion en niveaux de gris)
- Convertit les labels en classes
- Planifie et préprocesse le dataset
- Entraîne le modèle avec la configuration spécifiée

### Inférence
```bash
python infer_nnunet.py
```
Le script d'inférence :
- Prétraite les images d'entrée
- Effectue la prédiction
- Post-traite les masques prédits
- Génère des visualisations avec overlays

### Export du Modèle
```bash
python export_model_zip.py
```
Le script d'export :
- Exporte le modèle entraîné au format ZIP
- Inclut les métadonnées et configurations nécessaires
- Prépare le modèle pour le déploiement

### Visualisation
Les outils de visualisation sont disponibles dans le module `utils/` :
- `overlay_manager.py` : Gestion des overlays et visualisations avancées
- `visualisation.py` : Outils de visualisation de base

## Configuration
Les paramètres principaux sont configurables dans les scripts :
- `DATASET_ID` : Identifiant du dataset
- `CONFIGURATION` : Configuration du modèle (2d ou 3d_fullres)
- `FOLD` : Fold pour la validation croisée
- `EPOCHS` : Nombre d'époques d'entraînement
- `GPU_ID` : ID du GPU à utiliser

## Fonctionnalités
- Implémentation nnU-Net v2 pour la segmentation d'images de corrosion
- Pipeline complet de prétraitement et post-traitement
- Conversion automatique des images et labels
- Gestion avancée des overlays et visualisations
- Export du modèle entraîné pour déploiement
- Support multi-GPU
- Gestion automatique des versions de sortie
- Outils de vérification et validation des données

## Support
Pour toute question ou problème, veuillez créer une issue dans le repository GitLab.

## Licence
Propriétaire - Tous droits réservés
