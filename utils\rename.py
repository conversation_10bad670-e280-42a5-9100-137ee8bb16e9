import os
import re

def initial_rename(images_dir):
    """
    Première passe de renommage : convertit les noms de fichiers en format numérique séquentiel (0001.png, 0002.png, etc.)
    """
    # Liste tous les fichiers PNG et les trie
    png_files = sorted([f for f in os.listdir(images_dir) if f.lower().endswith('.png')])
    
    # Renomme chaque fichier avec un numéro séquentiel
    for idx, old_name in enumerate(png_files, start=1):
        new_name = f"{str(idx).zfill(4)}.png"
        src = os.path.join(images_dir, old_name)
        dst = os.path.join(images_dir, new_name)
        
        if src != dst:
            os.rename(src, dst)
            print(f"[RENAME] Image renommée (première passe) : {old_name} → {new_name}")

def rename_imagesTr(images_dir):
    """
    Renomme les images pour qu'elles soient au format nnU-Net : {id}_0000.png
    Ne renomme pas si le fichier est déjà conforme.
    """
    def extract_numeric_id(filename):
        match = re.search(r"(\d+)", filename)
        return match.group(1).zfill(4) if match else None

    for fname in sorted(os.listdir(images_dir)):
        if fname.lower().endswith(".png"):
            if fname.endswith("_0000.png"):
                print(f"⏩ Image déjà conforme : {fname}")
                continue

            numeric_id = extract_numeric_id(fname)
            if numeric_id:
                new_name = f"{numeric_id}_0000.png"
                src = os.path.join(images_dir, fname)
                dst = os.path.join(images_dir, new_name)
                if src != dst:
                    os.rename(src, dst)
                    print(f"[RENAME] Image renommée : {fname} → {new_name}")

def rename_labelsTr(labels_dir):
    """
    Renomme les masques pour qu'ils soient au format nnU-Net : {id}.png
    Ne renomme pas si déjà conforme (pas de suffixe, juste le numéro).
    """
    def extract_numeric_id(filename):
        match = re.search(r"(\d+)", filename)
        return match.group(1).zfill(4) if match else None

    for fname in sorted(os.listdir(labels_dir)):
        if fname.lower().endswith(".png"):
            if re.fullmatch(r"\d{4}\.png", fname):
                print(f"⏩ Masque déjà conforme : {fname}")
                continue

            numeric_id = extract_numeric_id(fname)
            if numeric_id:
                new_name = f"{numeric_id}.png"
                src = os.path.join(labels_dir, fname)
                dst = os.path.join(labels_dir, new_name)
                if src != dst:
                    os.rename(src, dst)
                    print(f"[RENAME] Masque renommé : {fname} → {new_name}")
