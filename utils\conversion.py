import os
import shutil
import numpy as np
from PIL import Image

def backup_folder(src_folder):
    """
    Crée un backup du dossier en le copiant dans <src_folder>_backup
    """
    backup_dir = src_folder.rstrip("\\/") + "_backup"
    if not os.path.exists(backup_dir):
        shutil.copytree(src_folder, backup_dir)
        print(f"📦 Backup créé : {backup_dir}")
    else:
        print(f"ℹ️ Backup déjà existant : {backup_dir}")
    return backup_dir

def convert_images_to_grayscale(images_dir):
    """
    Convertit les images en niveaux de gris (mode 'L') seulement si nécessaire.
    Sauvegarde les originaux dans un dossier *_backup.
    """
    backup_folder(images_dir)

    for fname in os.listdir(images_dir):
        if fname.lower().endswith(".png"):
            path = os.path.join(images_dir, fname)
            with Image.open(path) as img:
                if img.mode != "L":
                    img = img.convert("L")
                    img.save(path)
                    print(f"✅ Image convertie (grayscale) : {fname}")
                else:
                    print(f"⏩ Image déjà en grayscale : {fname}")

def convert_labels_to_classes(labels_dir, val_to_class):
    """
    Convertit les valeurs brutes des masques en entiers de classes (0,1,2...).
    Ne reconvertit pas si le masque contient déjà uniquement les classes attendues.
    """
    if val_to_class is None:
        raise ValueError("val_to_class requis")

    expected_classes = set(val_to_class.values())
    backup_folder(labels_dir)

    for fname in os.listdir(labels_dir):
        if fname.lower().endswith(".png"):
            path = os.path.join(labels_dir, fname)
            mask = np.array(Image.open(path).convert("L"))

            unique_vals = set(np.unique(mask))
            if unique_vals.issubset(expected_classes):
                print(f"⏩ Masque déjà converti : {fname}")
                continue

            class_mask = np.zeros_like(mask, dtype=np.uint8)
            for val, cls in val_to_class.items():
                class_mask[mask == val] = cls

            Image.fromarray(class_mask, mode="L").save(path)
            print(f"✅ Masque converti (classes) : {fname}")
