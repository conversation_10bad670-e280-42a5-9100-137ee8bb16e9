#!/usr/bin/env python3
"""
Script de test pour vérifier que les scripts modifiés acceptent bien les arguments en ligne de commande.
"""

import subprocess
import sys

def test_script_help(script_name):
    """Teste si un script affiche l'aide correctement."""
    print(f"\n=== Test de {script_name} ===")
    try:
        result = subprocess.run([sys.executable, script_name, "--help"], 
                              capture_output=True, text=True, timeout=10)
        print(f"Code de retour: {result.returncode}")
        if result.returncode == 0:
            print("✅ Le script accepte les arguments en ligne de commande")
            print("Aide disponible:")
            print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
        else:
            print("❌ Erreur lors de l'affichage de l'aide")
            print("STDERR:", result.stderr)
    except subprocess.TimeoutExpired:
        print("⏰ Timeout - le script prend trop de temps")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    print("🧪 Test des scripts modifiés pour l'interface graphique")
    
    scripts = [
        "infer_nnunet.py",
        "train_nnunet.py", 
        "export_model_zip.py"
    ]
    
    for script in scripts:
        test_script_help(script)
    
    print("\n✅ Tests terminés!")

if __name__ == "__main__":
    main()
