import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import subprocess
import threading
import os
import sys

class NNUNetGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("nnU-Net Launcher GUI")
        self.root.geometry("800x700")
        
        # Variables pour stocker les paramètres
        self.setup_variables()
        
        # Créer l'interface
        self.create_widgets()
        
        # Charger les valeurs par défaut
        self.load_defaults()
    
    def setup_variables(self):
        # Variables communes
        self.dataset_id = tk.StringVar()
        self.configuration = tk.StringVar()
        self.fold = tk.StringVar()
        self.epochs = tk.StringVar()
        self.gpu_id = tk.StringVar()
        
        # Variables pour inférence
        self.input_folder = tk.StringVar()
        self.base_output_root = tk.StringVar()
        
        # Variables pour entraînement
        self.save_npz = tk.BooleanVar()
        self.continue_training = tk.BooleanVar()
        self.num_gpus = tk.StringVar()
        
        # Variables pour export
        self.trainer_name = tk.StringVar()
        self.plans_name = tk.StringVar()
        self.output_zip_name = tk.StringVar()
    
    def create_widgets(self):
        # Notebook pour les onglets
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Onglet Inférence
        self.create_inference_tab(notebook)
        
        # Onglet Entraînement
        self.create_training_tab(notebook)
        
        # Onglet Export
        self.create_export_tab(notebook)
        
        # Zone de log commune
        self.create_log_area()
    
    def create_inference_tab(self, notebook):
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Inférence")
        
        # Paramètres principaux
        main_frame = ttk.LabelFrame(frame, text="Paramètres principaux")
        main_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(main_frame, text="Dataset ID:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(main_frame, textvariable=self.dataset_id, width=10).grid(row=0, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Configuration:").grid(row=0, column=2, sticky='w', padx=5, pady=2)
        config_combo = ttk.Combobox(main_frame, textvariable=self.configuration, values=["2d", "3d_fullres"], width=12)
        config_combo.grid(row=0, column=3, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Fold:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        fold_combo = ttk.Combobox(main_frame, textvariable=self.fold, values=["all", "0", "1", "2", "3", "4"], width=10)
        fold_combo.grid(row=1, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Epochs:").grid(row=1, column=2, sticky='w', padx=5, pady=2)
        epochs_combo = ttk.Combobox(main_frame, textvariable=self.epochs, values=["0", "5", "10", "20", "50"], width=12)
        epochs_combo.grid(row=1, column=3, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="GPU ID:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(main_frame, textvariable=self.gpu_id, width=10).grid(row=2, column=1, sticky='w', padx=5, pady=2)
        
        # Dossiers
        folders_frame = ttk.LabelFrame(frame, text="Dossiers")
        folders_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(folders_frame, text="Dossier d'entrée:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(folders_frame, textvariable=self.input_folder, width=50).grid(row=0, column=1, sticky='ew', padx=5, pady=2)
        ttk.Button(folders_frame, text="Parcourir", command=self.browse_input_folder).grid(row=0, column=2, padx=5, pady=2)
        
        ttk.Label(folders_frame, text="Dossier de sortie (base):").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(folders_frame, textvariable=self.base_output_root, width=50).grid(row=1, column=1, sticky='ew', padx=5, pady=2)
        ttk.Button(folders_frame, text="Parcourir", command=self.browse_output_folder).grid(row=1, column=2, padx=5, pady=2)
        
        folders_frame.columnconfigure(1, weight=1)
        
        # Bouton de lancement
        ttk.Button(frame, text="Lancer l'inférence", command=self.run_inference, style='Accent.TButton').pack(pady=10)
    
    def create_training_tab(self, notebook):
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Entraînement")
        
        # Paramètres principaux
        main_frame = ttk.LabelFrame(frame, text="Paramètres principaux")
        main_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(main_frame, text="Dataset ID:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(main_frame, textvariable=self.dataset_id, width=10).grid(row=0, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Configuration:").grid(row=0, column=2, sticky='w', padx=5, pady=2)
        config_combo = ttk.Combobox(main_frame, textvariable=self.configuration, values=["2d", "3d_fullres"], width=12)
        config_combo.grid(row=0, column=3, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Fold:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        fold_combo = ttk.Combobox(main_frame, textvariable=self.fold, values=["all", "0", "1", "2", "3", "4"], width=10)
        fold_combo.grid(row=1, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Epochs:").grid(row=1, column=2, sticky='w', padx=5, pady=2)
        epochs_combo = ttk.Combobox(main_frame, textvariable=self.epochs, values=["5", "10", "20", "50", "100"], width=12)
        epochs_combo.grid(row=1, column=3, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="GPU ID:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(main_frame, textvariable=self.gpu_id, width=10).grid(row=2, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Nombre de GPUs:").grid(row=2, column=2, sticky='w', padx=5, pady=2)
        ttk.Entry(main_frame, textvariable=self.num_gpus, width=12).grid(row=2, column=3, sticky='w', padx=5, pady=2)
        
        # Options
        options_frame = ttk.LabelFrame(frame, text="Options")
        options_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Checkbutton(options_frame, text="Sauvegarder NPZ", variable=self.save_npz).pack(anchor='w', padx=5, pady=2)
        ttk.Checkbutton(options_frame, text="Continuer l'entraînement", variable=self.continue_training).pack(anchor='w', padx=5, pady=2)
        
        # Bouton de lancement
        ttk.Button(frame, text="Lancer l'entraînement", command=self.run_training, style='Accent.TButton').pack(pady=10)
    
    def create_export_tab(self, notebook):
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Export Modèle")
        
        # Paramètres
        main_frame = ttk.LabelFrame(frame, text="Paramètres d'export")
        main_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(main_frame, text="Dataset ID:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(main_frame, textvariable=self.dataset_id, width=10).grid(row=0, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Configuration:").grid(row=0, column=2, sticky='w', padx=5, pady=2)
        config_combo = ttk.Combobox(main_frame, textvariable=self.configuration, values=["2d", "3d_fullres"], width=12)
        config_combo.grid(row=0, column=3, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Fold:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        fold_combo = ttk.Combobox(main_frame, textvariable=self.fold, values=["all", "0", "1", "2", "3", "4"], width=10)
        fold_combo.grid(row=1, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Nom du trainer:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(main_frame, textvariable=self.trainer_name, width=25).grid(row=2, column=1, columnspan=2, sticky='ew', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Plans name:").grid(row=3, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(main_frame, textvariable=self.plans_name, width=25).grid(row=3, column=1, columnspan=2, sticky='ew', padx=5, pady=2)
        
        ttk.Label(main_frame, text="Nom du fichier ZIP:").grid(row=4, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(main_frame, textvariable=self.output_zip_name, width=25).grid(row=4, column=1, columnspan=2, sticky='ew', padx=5, pady=2)
        
        main_frame.columnconfigure(1, weight=1)
        
        # Bouton de lancement
        ttk.Button(frame, text="Exporter le modèle", command=self.run_export, style='Accent.TButton').pack(pady=10)
    
    def create_log_area(self):
        log_frame = ttk.LabelFrame(self.root, text="Logs")
        log_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, wrap=tk.WORD)
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Bouton pour effacer les logs
        ttk.Button(log_frame, text="Effacer les logs", command=self.clear_logs).pack(pady=5)
    
    def load_defaults(self):
        # Valeurs par défaut basées sur les scripts existants
        self.dataset_id.set("018")
        self.configuration.set("2d")
        self.fold.set("all")
        self.epochs.set("5")
        self.gpu_id.set("0")
        self.num_gpus.set("1")
        self.save_npz.set(True)
        self.continue_training.set(False)
        self.trainer_name.set("nnUNetTrainer_5epochs")
        self.plans_name.set("nnUNetPlans")
        self.output_zip_name.set("model_018_2d.zip")
        
        # Dossiers par défaut
        self.input_folder.set(r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\imagesTestInference")
        self.base_output_root.set(r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference")
    
    def browse_input_folder(self):
        folder = filedialog.askdirectory(title="Sélectionner le dossier d'entrée")
        if folder:
            self.input_folder.set(folder)
    
    def browse_output_folder(self):
        folder = filedialog.askdirectory(title="Sélectionner le dossier de sortie de base")
        if folder:
            self.base_output_root.set(folder)
    
    def log_message(self, message):
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def clear_logs(self):
        self.log_text.delete(1.0, tk.END)
    
    def run_command_in_thread(self, script_name, params):
        def run():
            try:
                self.log_message(f"🚀 Lancement de {script_name}...")
                self.log_message(f"Paramètres: {params}")
                
                # Construire la commande Python
                cmd = [sys.executable, script_name] + params
                
                # Lancer le processus
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )
                
                # Lire la sortie en temps réel
                for line in process.stdout:
                    self.log_message(line.rstrip())
                
                process.wait()
                
                if process.returncode == 0:
                    self.log_message(f"✅ {script_name} terminé avec succès!")
                else:
                    self.log_message(f"❌ {script_name} a échoué avec le code {process.returncode}")
                    
            except Exception as e:
                self.log_message(f"❌ Erreur lors de l'exécution: {str(e)}")
        
        thread = threading.Thread(target=run)
        thread.daemon = True
        thread.start()

    def run_inference(self):
        # Vérifier les paramètres requis
        if not self.input_folder.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un dossier d'entrée")
            return

        if not self.base_output_root.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un dossier de sortie de base")
            return

        # Préparer les paramètres
        params = [
            "--dataset_id", self.dataset_id.get(),
            "--configuration", self.configuration.get(),
            "--fold", self.fold.get(),
            "--epochs", self.epochs.get(),
            "--gpu_id", self.gpu_id.get(),
            "--input_folder", self.input_folder.get(),
            "--base_output_root", self.base_output_root.get()
        ]

        self.run_command_in_thread("infer_nnunet.py", params)

    def run_training(self):
        # Préparer les paramètres
        params = [
            "--dataset_id", self.dataset_id.get(),
            "--configuration", self.configuration.get(),
            "--fold", self.fold.get(),
            "--epochs", self.epochs.get(),
            "--gpu_id", self.gpu_id.get(),
            "--num_gpus", self.num_gpus.get()
        ]

        if self.save_npz.get():
            params.append("--save_npz")

        if self.continue_training.get():
            params.append("--continue_training")

        self.run_command_in_thread("train_nnunet.py", params)

    def run_export(self):
        # Vérifier les paramètres requis
        if not self.trainer_name.get():
            messagebox.showerror("Erreur", "Veuillez spécifier le nom du trainer")
            return

        if not self.output_zip_name.get():
            messagebox.showerror("Erreur", "Veuillez spécifier le nom du fichier ZIP")
            return

        # Préparer les paramètres
        params = [
            "--dataset_id", self.dataset_id.get(),
            "--configuration", self.configuration.get(),
            "--fold", self.fold.get(),
            "--trainer_name", self.trainer_name.get(),
            "--plans_name", self.plans_name.get(),
            "--output_zip_name", self.output_zip_name.get()
        ]

        self.run_command_in_thread("export_model_zip.py", params)


def main():
    root = tk.Tk()
    app = NNUNetGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
