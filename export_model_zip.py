import subprocess
import argparse

def parse_arguments():
    parser = argparse.ArgumentParser(description="nnU-Net Model Export Script")
    parser.add_argument("--dataset_id", default="014", help="Dataset ID")
    parser.add_argument("--configuration", default="2d", choices=["2d", "3d_fullres"], help="Configuration")
    parser.add_argument("--fold", default="all", help="Fold (all, 0-4)")
    parser.add_argument("--trainer_name", default="nnUNetTrainer_5epochs", help="Trainer name")
    parser.add_argument("--plans_name", default="nnUNetPlans", help="Plans name")
    parser.add_argument("--output_zip_name", required=True, help="Output ZIP filename")
    return parser.parse_args()

def main():
    # Parse arguments
    args = parse_arguments()

    # === PARAMÈTRES MODIFIABLES ===
    DATASET_ID = args.dataset_id
    CONFIGURATION = args.configuration
    FOLD = args.fold
    TRAINER_NAME = args.trainer_name
    PLANS_NAME = args.plans_name
    OUTPUT_ZIP_NAME = args.output_zip_name

    # === CONSTRUCTION DE LA COMMANDE ===
    cmd = (
        f"nnUNetv2_export_model_to_zip "
        f"-d {DATASET_ID} "
        f"-c {CONFIGURATION} "
        f"-tr {TRAINER_NAME} "
        f"-p {PLANS_NAME} "
        f"-f {FOLD} "
        f"-o {OUTPUT_ZIP_NAME}"
    )

    # === AFFICHAGE ET EXÉCUTION ===
    print("🟢 Commande générée :")
    print(cmd)
    print("\n⏳ Exportation en cours...\n")

    try:
        subprocess.run(cmd, shell=True, check=True)
        print(f"\n✅ Exportation réussie : {OUTPUT_ZIP_NAME}")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Échec de l'exportation.\nErreur : {e}")


if __name__ == "__main__":
    main()
