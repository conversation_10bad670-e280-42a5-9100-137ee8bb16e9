import os
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

def reconvert_predictions(input_dir, output_dir, class_to_value=None):
    """
    Convertit les masques de prédiction nnU-Net (0, 1, 2...) en valeurs brutes (51, 76, 102, 255...).
    """
    if class_to_value is None:
        class_to_value = {
            0: 51,    # fond
            1: 76,    # défaut 1
            2: 102,   # défaut 2
            3: 255    # défaut principal
        }

    os.makedirs(output_dir, exist_ok=True)

    for fname in os.listdir(input_dir):
        if not fname.lower().endswith(".png"):
            print(f"⏭️ Ignoré (non-PNG) : {fname}")
            continue

        path = os.path.join(input_dir, fname)
        class_mask = np.array(Image.open(path).convert("L"))
        value_mask = np.zeros_like(class_mask, dtype=np.uint8)

        for cls, val in class_to_value.items():
            value_mask[class_mask == cls] = val

        Image.fromarray(value_mask, mode="L").save(os.path.join(output_dir, fname))
        print(f"[CONVERT] Converti : {fname}")

def generate_side_by_side(image_folder, mask_folder, output_dir):
    """
    Génère des images comparatives (image originale + masque prédiction) côte à côte.
    """
    os.makedirs(output_dir, exist_ok=True)

    for fname in sorted(os.listdir(mask_folder)):
        if not fname.endswith(".png"):
            continue

        basename = os.path.splitext(fname)[0]
        image_file = os.path.join(image_folder, f"{basename}_0000.png")
        mask_file = os.path.join(mask_folder, fname)

        if not os.path.exists(image_file):
            print(f"⛔ Image manquante : {image_file}")
            continue

        img_a = mpimg.imread(image_file)
        img_b = mpimg.imread(mask_file)

        fig, axs = plt.subplots(1, 2, figsize=(10, 5))
        axs[0].imshow(img_a, cmap='gray')
        axs[0].set_title('Image originale')
        axs[0].axis('off')

        axs[1].imshow(img_b, cmap='gray')
        axs[1].set_title('Masque prédiction')
        axs[1].axis('off')

        plt.tight_layout()
        save_path = os.path.join(output_dir, f"side_by_side_{basename}.png")
        plt.savefig(save_path)
        plt.close(fig)
        print(f"[SAVE] Comparaison sauvegardée : {save_path}")
